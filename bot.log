2025-07-30 00:48:23,367 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:48:23,367 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:48:23,369 - conversation_memory - INFO - Memory file conversations.json not found, starting fresh
2025-07-30 00:48:23,409 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,409 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,409 - config - INFO - Shutting down bot...
2025-07-30 00:48:23,410 - config - INFO - Bot shutdown complete
2025-07-30 00:48:23,410 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,410 - __main__ - ERROR - Bo<PERSON> crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,086 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:49:56,086 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:49:56,087 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:49:56,124 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,124 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,124 - config - INFO - Shutting down bot...
2025-07-30 00:49:56,124 - config - INFO - Bot shutdown complete
2025-07-30 00:49:56,124 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,125 - __main__ - ERROR - Bot crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
