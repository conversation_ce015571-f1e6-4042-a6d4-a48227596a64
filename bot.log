2025-07-30 00:48:23,367 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:48:23,367 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:48:23,369 - conversation_memory - INFO - Memory file conversations.json not found, starting fresh
2025-07-30 00:48:23,409 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,409 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,409 - config - INFO - Shutting down bot...
2025-07-30 00:48:23,410 - config - INFO - Bot shutdown complete
2025-07-30 00:48:23,410 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:48:23,410 - __main__ - ERROR - Bo<PERSON> crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,086 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:49:56,086 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:49:56,087 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:49:56,124 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,124 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,124 - config - INFO - Shutting down bot...
2025-07-30 00:49:56,124 - config - INFO - Bot shutdown complete
2025-07-30 00:49:56,124 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:49:56,125 - __main__ - ERROR - Bot crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:52:15,059 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:52:15,059 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:52:15,059 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:52:15,096 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:52:15,096 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:52:15,096 - config - INFO - Shutting down bot...
2025-07-30 00:52:15,096 - config - INFO - Bot shutdown complete
2025-07-30 00:52:15,096 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:52:15,097 - __main__ - ERROR - Bot crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:53:19,889 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:53:19,889 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:53:19,890 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:53:19,927 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:53:19,927 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:53:19,927 - config - INFO - Shutting down bot...
2025-07-30 00:53:19,928 - config - INFO - Bot shutdown complete
2025-07-30 00:53:19,928 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:53:19,928 - __main__ - ERROR - Bot crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:54:36,903 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:54:36,903 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:54:36,904 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:54:36,941 - config - ERROR - Failed to initialize bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:54:36,941 - config - ERROR - Error running bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:54:36,942 - config - INFO - Bot shutdown complete
2025-07-30 00:54:36,942 - config - ERROR - Failed to start bot: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:54:36,942 - __main__ - ERROR - Bot crashed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-07-30 00:56:45,122 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:56:45,123 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:56:45,124 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:56:45,186 - config - INFO - Bot initialized successfully
2025-07-30 00:56:45,186 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 00:56:45,186 - config - ERROR - Error running bot: There is no current event loop in thread 'MainThread'.
2025-07-30 00:56:45,187 - config - INFO - Bot shutdown complete
2025-07-30 00:56:45,187 - config - ERROR - Failed to start bot: There is no current event loop in thread 'MainThread'.
2025-07-30 00:56:45,187 - __main__ - ERROR - Bot crashed: There is no current event loop in thread 'MainThread'.
2025-07-30 00:57:28,329 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:57:28,329 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:57:28,330 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:57:28,393 - config - INFO - Bot initialized successfully
2025-07-30 00:57:28,393 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 00:58:15,386 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:58:15,387 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:58:15,449 - config - INFO - Bot initialized successfully
2025-07-30 00:58:15,694 - ai_processor - INFO - Gemini AI model initialized successfully
2025-07-30 00:58:15,694 - config - INFO - Starting Customer Support Bot...
2025-07-30 00:58:15,695 - conversation_memory - INFO - Loaded 0 conversations from conversations.json
2025-07-30 00:58:15,746 - config - INFO - Bot initialized successfully
2025-07-30 00:58:15,746 - config - INFO - Bot is running! Press Ctrl+C to stop.
2025-07-30 00:58:17,239 - telegram.ext.Updater - ERROR - Exception happened while polling for updates.
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 750, in _network_loop_retry
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 744, in do_action
    return action_cb_task.result()
           ~~~~~~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_updater.py", line 371, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 650, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<9 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 4480, in get_updates
    await self._post(
    ^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    ),
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 619, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_extbot.py", line 354, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/_bot.py", line 648, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
    ...<6 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 202, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/request/_baserequest.py", line 385, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-30 00:58:20,457 - __main__ - INFO - Received signal 15, shutting down...
2025-07-30 00:58:20,458 - config - INFO - Bot operation cancelled
2025-07-30 00:58:20,459 - config - ERROR - Error running bot: This Application is still running!
2025-07-30 00:58:20,461 - config - INFO - Bot shutdown complete
2025-07-30 00:58:20,462 - config - ERROR - Failed to start bot: This Application is still running!
2025-07-30 00:58:20,462 - asyncio - ERROR - unhandled exception during asyncio.run() shutdown
task: <Task finished name='Task-1' coro=<main() done, defined at /Users/<USER>/chatbot/telegram_bot.py:298> exception=RuntimeError('This Application is still running!')>
Traceback (most recent call last):
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 712, in run_until_complete
    self.run_forever()
    ~~~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 683, in run_forever
    self._run_once()
    ~~~~~~~~~~~~~~^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/base_events.py", line 2004, in _run_once
    event_list = self._selector.select(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py", line 548, in select
    kev_list = self._selector.control(None, max_ev, timeout)
  File "/Users/<USER>/chatbot/run_bot.py", line 16, in signal_handler
    sys.exit(0)
    ~~~~~~~~^^^
SystemExit: 0

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/chatbot/telegram_bot.py", line 306, in main
    await bot.run()
  File "/Users/<USER>/chatbot/telegram_bot.py", line 273, in run
    async with self.application:
               ^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 385, in __aexit__
    await self.shutdown()
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/telegram/ext/_application.py", line 535, in shutdown
    raise RuntimeError("This Application is still running!")
RuntimeError: This Application is still running!
