"""
Conversation memory system for tracking user interactions.
"""
import json
import asyncio
import aiofiles
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from config import Config

logger = logging.getLogger(__name__)

class ConversationMemory:
    """Manages conversation history and user context."""
    
    def __init__(self, memory_file: str = None):
        self.memory_file = memory_file or Config.CONVERSATION_MEMORY_FILE
        self.conversations: Dict[str, Dict] = {}
        self.lock = asyncio.Lock()
        
    async def load_conversations(self):
        """Load conversations from file."""
        try:
            async with aiofiles.open(self.memory_file, 'r', encoding='utf-8') as f:
                content = await f.read()
                self.conversations = json.loads(content) if content else {}
                logger.info(f"Loaded {len(self.conversations)} conversations from {self.memory_file}")
        except FileNotFoundError:
            logger.info(f"Memory file {self.memory_file} not found, starting fresh")
            self.conversations = {}
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing memory file: {e}")
            self.conversations = {}
        except Exception as e:
            logger.error(f"Error loading conversations: {e}")
            self.conversations = {}
    
    async def save_conversations(self):
        """Save conversations to file."""
        try:
            async with self.lock:
                async with aiofiles.open(self.memory_file, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(self.conversations, indent=2, ensure_ascii=False))
                logger.debug(f"Saved {len(self.conversations)} conversations to {self.memory_file}")
        except Exception as e:
            logger.error(f"Error saving conversations: {e}")
    
    async def add_message(self, user_id: str, message: str, is_user: bool = True, metadata: Dict = None):
        """Add a message to user's conversation history."""
        async with self.lock:
            user_id = str(user_id)
            
            if user_id not in self.conversations:
                self.conversations[user_id] = {
                    'messages': [],
                    'first_interaction': datetime.now().isoformat(),
                    'last_interaction': datetime.now().isoformat(),
                    'user_info': {},
                    'context': {}
                }
            
            # Update last interaction time
            self.conversations[user_id]['last_interaction'] = datetime.now().isoformat()
            
            # Add message
            message_data = {
                'timestamp': datetime.now().isoformat(),
                'message': message,
                'is_user': is_user,
                'metadata': metadata or {}
            }
            
            self.conversations[user_id]['messages'].append(message_data)
            
            # Limit conversation history
            max_history = Config.MAX_CONVERSATION_HISTORY
            if len(self.conversations[user_id]['messages']) > max_history:
                self.conversations[user_id]['messages'] = self.conversations[user_id]['messages'][-max_history:]
            
            # Save after each message
            await self.save_conversations()
    
    async def get_conversation_history(self, user_id: str, limit: int = 10) -> List[Dict]:
        """Get recent conversation history for a user."""
        user_id = str(user_id)
        
        if user_id not in self.conversations:
            return []
        
        messages = self.conversations[user_id]['messages']
        return messages[-limit:] if limit > 0 else messages
    
    async def get_user_context(self, user_id: str) -> Dict:
        """Get user context and metadata."""
        user_id = str(user_id)
        
        if user_id not in self.conversations:
            return {}
        
        return {
            'first_interaction': self.conversations[user_id].get('first_interaction'),
            'last_interaction': self.conversations[user_id].get('last_interaction'),
            'total_messages': len(self.conversations[user_id]['messages']),
            'user_info': self.conversations[user_id].get('user_info', {}),
            'context': self.conversations[user_id].get('context', {})
        }
    
    async def update_user_info(self, user_id: str, user_info: Dict):
        """Update user information."""
        async with self.lock:
            user_id = str(user_id)
            
            if user_id not in self.conversations:
                await self.add_message(user_id, "", is_user=True)  # Initialize conversation
            
            self.conversations[user_id]['user_info'].update(user_info)
            await self.save_conversations()
    
    async def set_context(self, user_id: str, context_key: str, context_value: Any):
        """Set context information for a user."""
        async with self.lock:
            user_id = str(user_id)
            
            if user_id not in self.conversations:
                await self.add_message(user_id, "", is_user=True)  # Initialize conversation
            
            self.conversations[user_id]['context'][context_key] = context_value
            await self.save_conversations()
    
    async def get_context(self, user_id: str, context_key: str) -> Any:
        """Get context information for a user."""
        user_id = str(user_id)
        
        if user_id not in self.conversations:
            return None
        
        return self.conversations[user_id]['context'].get(context_key)
    
    async def cleanup_old_conversations(self, days: int = 30):
        """Remove conversations older than specified days."""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        async with self.lock:
            users_to_remove = []
            
            for user_id, conversation in self.conversations.items():
                last_interaction = datetime.fromisoformat(conversation['last_interaction'])
                if last_interaction < cutoff_date:
                    users_to_remove.append(user_id)
            
            for user_id in users_to_remove:
                del self.conversations[user_id]
            
            if users_to_remove:
                logger.info(f"Cleaned up {len(users_to_remove)} old conversations")
                await self.save_conversations()
    
    async def get_conversation_summary(self, user_id: str) -> str:
        """Generate a summary of the conversation for context."""
        history = await self.get_conversation_history(user_id, limit=5)
        context = await self.get_user_context(user_id)
        
        if not history:
            return "New user - no previous conversation history."
        
        summary_parts = []
        
        # Add user context
        if context.get('total_messages', 0) > 5:
            summary_parts.append(f"Returning user with {context['total_messages']} total messages.")
        
        # Add recent conversation context
        recent_messages = []
        for msg in history[-3:]:  # Last 3 messages
            role = "User" if msg['is_user'] else "Bot"
            recent_messages.append(f"{role}: {msg['message'][:100]}...")
        
        if recent_messages:
            summary_parts.append("Recent conversation:\n" + "\n".join(recent_messages))
        
        return "\n\n".join(summary_parts) if summary_parts else "No significant conversation history."
