"""
Main Telegram bot implementation for automated customer support.
"""
import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional

from telegram import Update, User
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.error import Telegram<PERSON><PERSON>r, NetworkError, TimedOut

from config import Config, setup_logging
from conversation_memory import ConversationMemory
from ai_processor import AIProcessor

logger = setup_logging()

class CustomerSupportBot:
    """Main customer support bot class."""
    
    def __init__(self):
        self.memory = ConversationMemory()
        self.ai_processor = AIProcessor()
        self.application = None
        self.last_request_time = 0
        
    async def initialize(self):
        """Initialize the bot and load conversation memory."""
        try:
            # Load conversation memory
            await self.memory.load_conversations()

            # Build application with proper configuration for v20.7
            builder = Application.builder()
            builder.token(Config.TELEGRAM_BOT_TOKEN)
            self.application = builder.build()

            # Add handlers
            self.application.add_handler(<PERSON>Handler("start", self.start_command))
            self.application.add_handler(<PERSON><PERSON>andler("help", self.help_command))
            self.application.add_handler(CommandHandler("status", self.status_command))
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

            # Add error handler
            self.application.add_error_handler(self.error_handler)

            logger.info("Bot initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            raise
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        try:
            user = update.effective_user
            user_id = str(user.id)
            
            # Update user info
            user_info = {
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code
            }
            await self.memory.update_user_info(user_id, user_info)
            
            # Generate greeting
            greeting = await self.ai_processor.generate_greeting_response(user.first_name)
            
            # Add to conversation memory
            await self.memory.add_message(user_id, "/start", is_user=True)
            await self.memory.add_message(user_id, greeting, is_user=False)
            
            await update.message.reply_text(greeting)
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await self.send_error_response(update)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        try:
            help_text = f"""🤖 **{Config.BOT_NAME}** - Customer Support Assistant

I'm here to help you with:
• General questions and inquiries
• Technical support and troubleshooting
• Product information and guidance
• Account-related questions

**How to use:**
Just send me a message describing your question or issue, and I'll do my best to help you!

**Commands:**
/start - Start a conversation with me
/help - Show this help message
/status - Check bot status

**Need human support?**
For complex issues, I can connect you with our human support team at {Config.SUPPORT_CONTACT}

Feel free to ask me anything! 😊"""

            user_id = str(update.effective_user.id)
            await self.memory.add_message(user_id, "/help", is_user=True)
            await self.memory.add_message(user_id, help_text, is_user=False)
            
            await update.message.reply_text(help_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await self.send_error_response(update)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        try:
            user_id = str(update.effective_user.id)
            context_info = await self.memory.get_user_context(user_id)
            
            status_text = f"""📊 **Bot Status**

✅ Bot is online and operational
🕐 Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Your conversation stats:**
• First interaction: {context_info.get('first_interaction', 'N/A')}
• Total messages: {context_info.get('total_messages', 0)}
• Last interaction: {context_info.get('last_interaction', 'N/A')}

Need help? Just send me a message! 🚀"""

            await self.memory.add_message(user_id, "/status", is_user=True)
            await self.memory.add_message(user_id, status_text, is_user=False)
            
            await update.message.reply_text(status_text, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error in status command: {e}")
            await self.send_error_response(update)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle incoming text messages."""
        try:
            # Simple rate limiting
            import time
            current_time = time.time()
            if current_time - self.last_request_time < (60 / Config.MAX_REQUESTS_PER_MINUTE):
                await asyncio.sleep(60 / Config.MAX_REQUESTS_PER_MINUTE)
            self.last_request_time = current_time

            await self.process_user_message(update, context)

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self.send_error_response(update)
    
    async def process_user_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Process user message and generate response."""
        user = update.effective_user
        user_id = str(user.id)
        user_message = update.message.text
        
        logger.info(f"Processing message from user {user_id}: {user_message[:100]}...")
        
        # Add user message to memory
        await self.memory.add_message(user_id, user_message, is_user=True)
        
        # Get conversation context
        conversation_summary = await self.memory.get_conversation_summary(user_id)
        user_context = await self.memory.get_user_context(user_id)
        
        # Analyze intent
        intent_data = await self.ai_processor.analyze_intent(user_message)
        
        # Check if escalation is needed
        should_escalate = await self.ai_processor.should_escalate_to_human(intent_data, user_message)
        
        if should_escalate:
            response = await self.generate_escalation_response(intent_data)
        else:
            # Generate AI response
            user_info = {
                'name': user.first_name,
                'username': user.username,
                'total_messages': user_context.get('total_messages', 0)
            }
            
            response = await self.ai_processor.generate_response(
                user_message, 
                conversation_summary, 
                user_info
            )
        
        # Add response delay for natural feel
        await asyncio.sleep(Config.RESPONSE_DELAY_SECONDS)
        
        # Send response
        await update.message.reply_text(response)
        
        # Add bot response to memory
        await self.memory.add_message(user_id, response, is_user=False, metadata={
            'intent': intent_data,
            'escalated': should_escalate
        })
        
        logger.info(f"Sent response to user {user_id}")
    
    async def generate_escalation_response(self, intent_data: Dict) -> str:
        """Generate response for escalation to human support."""
        urgency = intent_data.get('urgency', 'medium')
        sentiment = intent_data.get('sentiment', 'neutral')
        
        if urgency == 'high' or sentiment == 'negative':
            response = f"""I understand this is an important matter for you, and I want to make sure you get the best possible assistance.

For this type of inquiry, I'd like to connect you with our human support team who can provide specialized help.

Please contact our support team at {Config.SUPPORT_CONTACT} - they'll be able to assist you promptly and thoroughly.

In the meantime, if you have any other questions I can help with, please feel free to ask! 🙏"""
        else:
            response = f"""Thank you for your message! While I can help with many questions, this particular inquiry would be best handled by our human support team.

Please reach out to {Config.SUPPORT_CONTACT} for personalized assistance with this matter.

Is there anything else I can help you with in the meantime? 😊"""
        
        return response
    
    async def send_error_response(self, update: Update):
        """Send a generic error response to the user."""
        error_message = f"""I apologize, but I'm experiencing some technical difficulties right now. 😔

Please try again in a moment, or contact our human support team at {Config.SUPPORT_CONTACT} if you need immediate assistance.

Thank you for your patience!"""
        
        try:
            await update.message.reply_text(error_message)
        except Exception as e:
            logger.error(f"Failed to send error response: {e}")
    
    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors that occur during bot operation."""
        logger.error(f"Exception while handling an update: {context.error}")
        
        # Handle specific error types
        if isinstance(context.error, NetworkError):
            logger.warning("Network error occurred, will retry")
        elif isinstance(context.error, TimedOut):
            logger.warning("Request timed out")
        elif isinstance(context.error, TelegramError):
            logger.error(f"Telegram error: {context.error}")
        
        # Try to send error message to user if update is available
        if update and hasattr(update, 'message') and update.message:
            await self.send_error_response(update)
    
    async def run(self):
        """Start the bot."""
        try:
            logger.info(f"Starting {Config.BOT_NAME}...")

            # Initialize the bot
            await self.initialize()

            # Run the bot with polling
            logger.info("Bot is running! Press Ctrl+C to stop.")

            # Use the async context manager approach
            async with self.application:
                await self.application.start()
                await self.application.updater.start_polling(drop_pending_updates=True)

                # Keep running until interrupted
                try:
                    await asyncio.Event().wait()
                except asyncio.CancelledError:
                    logger.info("Bot operation cancelled")

        except KeyboardInterrupt:
            logger.info("Received stop signal")
        except Exception as e:
            logger.error(f"Error running bot: {e}")
            raise
        finally:
            # Save conversation memory on shutdown
            try:
                await self.memory.save_conversations()
                logger.info("Bot shutdown complete")
            except Exception as e:
                logger.error(f"Error during shutdown: {e}")



async def main():
    """Main function to run the bot."""
    try:
        # Validate configuration
        Config.validate()

        # Create and run bot
        bot = CustomerSupportBot()
        await bot.run()

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Failed to start bot: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
